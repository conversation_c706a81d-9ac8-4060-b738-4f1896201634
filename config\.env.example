# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Model Configuration
OPENAI_MODEL=gpt-3.5-turbo
EMBEDDING_MODEL=text-embedding-ada-002

# Vector Database Configuration
VECTOR_DB_TYPE=chroma
VECTOR_DB_PATH=./data/vector_db

# Application Configuration
MAX_TOKENS=2000
TEMPERATURE=0.7
TOP_P=1.0
TOP_K=50

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Feature Flags
ENABLE_FUNCTION_CALLING=true
ENABLE_STRUCTURED_OUTPUT=true
ENABLE_TOKEN_LOGGING=true
